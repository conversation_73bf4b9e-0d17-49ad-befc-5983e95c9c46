"use client";
import { useEditorSafe } from "@/contexts/EditorContext";
import { SectionProps } from "@/lib/types";

import { Skill } from "@/lib/types";
import { Plus, Trash2, Code, Database, Server, Palette, Settings, Layers } from "lucide-react";

interface SkillItemProps {
    skill: Skill;
    isEditing: boolean;
    onUpdate: (id: string, field: keyof Skill, value: string) => void;
    onDelete: (id: string) => void;
}

const getCategoryIcon = (category: Skill['category']) => {
    switch (category) {
        case 'web-development': return <Code className="theme-modern-skill-category-icon" />;
        case 'mobile-development': return <Server className="theme-modern-skill-category-icon" />;
        case 'design': return <Palette className="theme-modern-skill-category-icon" />;
        case 'data-science': return <Database className="theme-modern-skill-category-icon" />;
        case 'devops': return <Settings className="theme-modern-skill-category-icon" />;
        case 'marketing': return <Layers className="theme-modern-skill-category-icon" />;
        case 'business': return <Layers className="theme-modern-skill-category-icon" />;
        default: return <Layers className="theme-modern-skill-category-icon" />;
    }
};

const getCategoryLabel = (category: Skill['category']) => {
    switch (category) {
        case 'web-development': return 'Web Development';
        case 'mobile-development': return 'Mobile Development';
        case 'design': return 'Design';
        case 'data-science': return 'Data Science';
        case 'devops': return 'DevOps';
        case 'marketing': return 'Marketing';
        case 'business': return 'Business';
        default: return 'Other';
    }
};

const SkillItem = ({ skill, isEditing, onUpdate, onDelete }: SkillItemProps) => {
    return (
        <div className="theme-modern-skill-item">
            <div className="theme-modern-skill-header">
                {isEditing ? (
                    <input
                        type="text"
                        value={skill.name}
                        onChange={(e) => onUpdate(skill.id, 'name', e.target.value)}
                        className="theme-modern-skill-name-input"
                        placeholder="Skill name"
                    />
                ) : (
                    <h3 className="theme-modern-skill-name">{skill.name}</h3>
                )}
                {isEditing && (
                    <button
                        onClick={() => onDelete(skill.id)}
                        className="theme-modern-skill-delete-btn"
                        title="Delete skill"
                    >
                        <Trash2 className="theme-modern-skill-delete-icon" />
                    </button>
                )}
            </div>

            {isEditing && (
                <div className="theme-modern-skill-controls">
                    <label className="theme-modern-skill-label">
                        Category:
                        <select
                            value={skill.category}
                            onChange={(e) => onUpdate(skill.id, 'category', e.target.value)}
                            className="theme-modern-skill-select"
                        >
                            <option value="web-development">Web Development</option>
                            <option value="mobile-development">Mobile Development</option>
                            <option value="design">Design</option>
                            <option value="data-science">Data Science</option>
                            <option value="devops">DevOps</option>
                            <option value="marketing">Marketing</option>
                            <option value="business">Business</option>
                            <option value="other">Other</option>
                        </select>
                    </label>
                </div>
            )}
        </div>
    );
};

export function ModernSkills({ isEditing, serverData }: SectionProps) {
    const context = useEditorSafe();
    const data = isEditing && context ? context.state.formData : serverData!;
    const dispatch = isEditing && context ? context.dispatch : null;

    const handleAddSkill = () => {
        if (dispatch) {
            const newSkill: Skill = {
                id: Date.now().toString(),
                name: 'New Skill',
                category: 'other'
            };

            // If we only have default skills, replace them with the new one
            const currentSkills = data.skills || [];
            const updatedSkills = currentSkills.length === 0
                ? [newSkill]
                : [...currentSkills, newSkill];

            dispatch({
                type: 'UPDATE_FIELD',
                payload: {
                    field: 'skills',
                    value: updatedSkills
                }
            });
        }
    };

    const handleUpdateSkill = (id: string, field: keyof Skill, value: string) => {
        if (dispatch) {
            const currentSkills = data.skills || [];
            const updatedSkills = currentSkills.map(skill =>
                skill.id === id ? { ...skill, [field]: value } : skill
            );
            dispatch({
                type: 'UPDATE_FIELD',
                payload: {
                    field: 'skills',
                    value: updatedSkills
                }
            });
        }
    };

    const handleDeleteSkill = (id: string) => {
        if (dispatch) {
            const currentSkills = data.skills || [];
            const updatedSkills = currentSkills.filter(skill => skill.id !== id);
            dispatch({
                type: 'UPDATE_FIELD',
                payload: {
                    field: 'skills',
                    value: updatedSkills
                }
            });
        }
    };

    // Always show at least some dummy skills if none exist
    const defaultSkills: Skill[] = [
        { id: 'default-skill-1', name: 'JavaScript', category: 'frontend' },
        { id: 'default-skill-2', name: 'React', category: 'frontend' },
        { id: 'default-skill-3', name: 'Node.js', category: 'backend' },
        { id: 'default-skill-4', name: 'Git', category: 'tools' }
    ];

    const skills = data.skills && data.skills.length > 0
        ? data.skills
        : defaultSkills;

    // Group skills by category with null check
    const groupedSkills = skills.reduce((acc, skill) => {
        if (!acc[skill.category]) {
            acc[skill.category] = [];
        }
        acc[skill.category].push(skill);
        return acc;
    }, {} as Record<Skill['category'], Skill[]>);

    return (
        <section id="skills" className="theme-modern-skills">
            <div className="theme-modern-skills-container">
                <h2 className="theme-modern-skills-title">Skills & Technologies</h2>
                
                <div className="theme-modern-skills-content">
                    {Object.entries(groupedSkills).map(([category, skills]) => (
                        <div key={category} className="theme-modern-skills-category">
                            <div className="theme-modern-skills-category-header">
                                {getCategoryIcon(category as Skill['category'])}
                                <h3 className="theme-modern-skills-category-title">
                                    {getCategoryLabel(category as Skill['category'])}
                                </h3>
                            </div>
                            <div className="theme-modern-skills-grid">
                                {skills.map((skill) => (
                                    <SkillItem
                                        key={skill.id}
                                        skill={skill}
                                        isEditing={isEditing}
                                        onUpdate={handleUpdateSkill}
                                        onDelete={handleDeleteSkill}
                                    />
                                ))}
                            </div>
                        </div>
                    ))}
                    
                    {isEditing && (
                        <div className="theme-modern-skills-add-container">
                            <button
                                onClick={handleAddSkill}
                                className="theme-modern-skills-add-btn"
                            >
                                <Plus className="theme-modern-skills-add-icon" />
                                Add Skill
                            </button>
                        </div>
                    )}
                </div>
            </div>
        </section>
    );
};
