"use client";
import { EditableText } from "@/components/ui/EditableText";
import { useEditorSafe } from "@/contexts/EditorContext";
import { SectionProps } from "@/lib/types";
import { Upload, Trash, ExternalLink, Loader2, Plus } from "lucide-react";
import { PortfolioImage } from "@/components/ui/PortfolioImage";
import { useIsExport } from "@/contexts/ExportContext";
import { Project } from "@/lib/types";

interface ProjectCardProps {
    project: Project;
    index: number;
    isEditing: boolean;
    isUploading: boolean;
    onUpdate: (index: number, field: keyof Project, value: string) => void;
    onImageUpload: (event: React.ChangeEvent<HTMLInputElement>, projectId: string) => void;
    onRemove: (projectId: string) => void;
    isExport: boolean;
}

const ProjectCard = ({ project, index, isEditing, isUploading, onUpdate, onImageUpload, onRemove }: ProjectCardProps) => (
    <div className="theme-modern-project-card">
        <div className="theme-modern-project-image-container">
            {isEditing && (
                <div className="theme-modern-project-upload-overlay">
                    <input
                        type="file"
                        accept="image/*"
                        onChange={(e) => onImageUpload(e, project.id)}
                        className="theme-modern-project-upload-input"
                        disabled={isUploading}
                    />
                    {isUploading ? (
                        <div className="theme-modern-project-upload-loading">
                            <Loader2 className="theme-modern-project-upload-icon theme-modern-spinner" />
                            <span className="theme-modern-project-upload-text">Uploading...</span>
                        </div>
                    ) : (
                        <div className="theme-modern-project-upload-content">
                            <Upload className="theme-modern-project-upload-icon" />
                            <span className="theme-modern-project-upload-text">Upload Image</span>
                        </div>
                    )}
                </div>
            )}
            <PortfolioImage
                isEditing={isEditing}
                src={project.imageUrl || 'https://placehold.co/400x200/1f2937/ffffff?text=Project'}
                alt={project.title}
                width={400}
                height={200}
                className={`theme-modern-project-image ${isUploading ? 'theme-modern-project-image-uploading' : ''}`}
            />

            {isUploading && (
                <div className="theme-modern-project-loading-overlay">
                    <div className="theme-modern-project-loading-content">
                        <Loader2 className="theme-modern-project-loading-spinner" />
                        <p className="theme-modern-project-loading-text">Uploading image...</p>
                    </div>
                </div>
            )}
        </div>
        <div className="theme-modern-project-content">
            <EditableText
                isEditing={isEditing}
                tagName="h3"
                className="theme-modern-project-title"
                initialValue={project.title}
                onSave={(value) => onUpdate(index, 'title', value)}
            />
            <EditableText
                isEditing={isEditing}
                tagName="p"
                className="theme-modern-project-description"
                initialValue={project.description}
                onSave={(value) => onUpdate(index, 'description', value)}
            />
            <div className="theme-modern-project-footer">
                {project.liveUrl && (
                    <a
                        href={project.liveUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="theme-modern-project-link"
                    >
                        <ExternalLink className="theme-modern-project-link-icon" />
                        View Project
                    </a>
                )}
                {isEditing && (
                    <button
                        onClick={() => onRemove(project.id)}
                        className="theme-modern-project-remove-btn"
                        aria-label="Remove project"
                    >
                        <Trash className="theme-modern-project-remove-icon" />
                    </button>
                )}
            </div>
        </div>
    </div>
);

export function ModernProjects({ isEditing, serverData, onImageUpload }: SectionProps) {
    const context = useEditorSafe();
    const isExport = useIsExport();

    const data = isEditing && context ? context.state.formData : serverData!;
    const dispatch = isEditing && context ? context.dispatch : null;

    const handleUpdate = (index: number, field: keyof Project, value: string) => {
        if (dispatch) {
            dispatch({ type: 'UPDATE_PROJECT', payload: { index, field, value } });
        }
    };

    const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>, projectId: string) => {
        const file = event.target.files?.[0];
        if (file && onImageUpload) {
            onImageUpload({ file, type: 'project', id: projectId });
        }
    };

    const removeProject = (projectId: string) => {
        if (dispatch) {
            dispatch({ type: 'DELETE_PROJECT', payload: { id: projectId } });
        }
    };

    const addProject = () => {
        if (dispatch) {
            dispatch({ type: 'ADD_PROJECT' });
        }
    };

    // Always show at least one dummy project if none exist
    const defaultProject: Project = {
        id: 'default-project',
        title: 'Sample Project',
        description: 'A showcase project demonstrating technical skills and creativity. Replace this with your own project details.',
        imageUrl: 'https://placehold.co/400x300/f3f4f6/6b7280?text=Project+Image',
        projectUrl: '#',
        githubUrl: '#'
    };

    const projects = data.projects && data.projects.length > 0
        ? data.projects
        : [defaultProject];

    return (
        <section id="projects" className="theme-modern-projects">
            <div className="theme-modern-projects-container">
                <div className="theme-modern-projects-header">
                    <h2 className="theme-modern-projects-title">Featured Work</h2>
                    <p className="theme-modern-projects-subtitle">
                        A showcase of projects that demonstrate technical expertise and creative problem-solving
                    </p>
                </div>

                <div className="theme-modern-projects-grid">
                    {data.projects.map((project, index) => (
                        <ProjectCard
                            key={project.id}
                            project={project}
                            index={index}
                            isEditing={isEditing}
                            isUploading={isEditing && context?.state.isUploading?.type === 'project' && context.state.isUploading.id === project.id}
                            onUpdate={handleUpdate}
                            onImageUpload={handleImageUpload}
                            onRemove={removeProject}
                            isExport={isExport}
                        />
                    ))}

                    {isEditing && (
                        <div className="theme-modern-project-add-card" onClick={addProject}>
                            <div className="theme-modern-project-add-content">
                                <Plus className="theme-modern-project-add-icon" />
                                <span className="theme-modern-project-add-text">Add Project</span>
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </section>
    );
};
