#!/usr/bin/env node

/**
 * Theme Creation CLI Tool
 * 
 * This script helps create a new theme with proper structure and boilerplate code.
 * 
 * Usage:
 *   node scripts/create-theme.js
 *   npm run create-theme
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

/**
 * Create theme directory structure
 */
function createThemeStructure(themeId, themeName) {
  const themeDir = `themes/${themeId}`;
  const componentsDir = `${themeDir}/components`;
  
  // Create directories
  if (!fs.existsSync(themeDir)) {
    fs.mkdirSync(themeDir, { recursive: true });
    console.log(`📁 Created directory: ${themeDir}`);
  }
  
  if (!fs.existsSync(componentsDir)) {
    fs.mkdirSync(componentsDir, { recursive: true });
    console.log(`📁 Created directory: ${componentsDir}`);
  }
  
  return { themeDir, componentsDir };
}

/**
 * Create CSS file
 */
function createCssFile(themeDir, themeId, themeName) {
  const cssContent = `/* ${themeName} Theme CSS */
/* Base styles and reset */
.theme-${themeId}-root {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  line-height: 1.6;
  color: #111827;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  background: white;
  overflow-x: hidden;
}

.theme-${themeId}-root *,
.theme-${themeId}-root *::before,
.theme-${themeId}-root *::after {
  box-sizing: inherit;
}

/* Container utilities */
.theme-${themeId}-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

@media (min-width: 640px) {
  .theme-${themeId}-container {
    padding: 0 1.5rem;
  }
}

@media (min-width: 1024px) {
  .theme-${themeId}-container {
    padding: 0 2rem;
  }
}

/* Navbar Styles */
.theme-${themeId}-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 50;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

/* Hero Section */
.theme-${themeId}-hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  padding: 2rem 0;
}

/* Add more theme-specific styles here */

/* Utility classes */
.theme-${themeId}-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  cursor: pointer;
  border: none;
  font-size: 1rem;
}

.theme-${themeId}-btn-primary {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  color: white;
}

.theme-${themeId}-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(59, 130, 246, 0.4);
}
`;
  
  const cssPath = `${themeDir}/${themeId}.css`;
  fs.writeFileSync(cssPath, cssContent);
  console.log(`🎨 Created CSS file: ${cssPath}`);
}

/**
 * Create main theme component
 */
function createMainComponent(componentsDir, themeId, themeName) {
  const componentName = themeName.replace(/\s+/g, '').replace(/[^a-zA-Z0-9]/g, '');
  
  const componentContent = `"use client";
import { ProfolifyThemeProps } from "@/lib/types";
import { ${componentName}Navbar } from "./${componentName}Navbar";
import { ${componentName}Hero } from "./${componentName}Hero";
import { ${componentName}About } from "./${componentName}About";
import { ${componentName}Experience } from "./${componentName}Experience";
import { ${componentName}Skills } from "./${componentName}Skills";
import { ${componentName}Projects } from "./${componentName}Projects";
import { ${componentName}Contact } from "./${componentName}Contact";
import { ${componentName}Footer } from "./${componentName}Footer";

export function ${componentName}Theme({ isEditing, serverData, onImageUpload }: ProfolifyThemeProps) {
    return (
        <div className="theme-${themeId}-root">
            <${componentName}Navbar isEditing={isEditing} serverData={serverData} />
            <main>
                <${componentName}Hero isEditing={isEditing} serverData={serverData} onImageUpload={onImageUpload} />
                <${componentName}About isEditing={isEditing} serverData={serverData} />
                <${componentName}Experience isEditing={isEditing} serverData={serverData} />
                <${componentName}Skills isEditing={isEditing} serverData={serverData} />
                <${componentName}Projects isEditing={isEditing} serverData={serverData} onImageUpload={onImageUpload} />
                <${componentName}Contact isEditing={isEditing} serverData={serverData} />
            </main>
            <${componentName}Footer isEditing={isEditing} serverData={serverData} />
        </div>
    );
}
`;
  
  const componentPath = `${componentsDir}/${componentName}Theme.tsx`;
  fs.writeFileSync(componentPath, componentContent);
  console.log(`⚛️  Created main component: ${componentPath}`);
  
  return componentName;
}

/**
 * Create basic component templates
 */
function createComponentTemplates(componentsDir, themeId, componentName) {
  const components = ['Navbar', 'Hero', 'About', 'Experience', 'Skills', 'Projects', 'Contact', 'Footer'];
  
  components.forEach(comp => {
    const componentContent = `"use client";
import { useEditorSafe } from "@/contexts/EditorContext";
import { SectionProps } from "@/lib/types";
import { EditableText } from "@/components/ui/EditableText";

export function ${componentName}${comp}({ isEditing, serverData }: SectionProps) {
    const context = useEditorSafe();
    const data = isEditing && context ? context.state.formData : serverData!;

    return (
        <section className="theme-${themeId}-${comp.toLowerCase()}">
            <div className="theme-${themeId}-container">
                <h2 className="theme-${themeId}-${comp.toLowerCase()}-title">
                    ${comp} Section
                </h2>
                <p className="theme-${themeId}-${comp.toLowerCase()}-content">
                    Add your ${comp.toLowerCase()} content here.
                </p>
            </div>
        </section>
    );
}
`;
    
    const componentPath = `${componentsDir}/${componentName}${comp}.tsx`;
    fs.writeFileSync(componentPath, componentContent);
    console.log(`📝 Created component: ${componentPath}`);
  });
}

/**
 * Main function
 */
async function main() {
  console.log('🎨 Welcome to Profolify Theme Creator!\n');
  
  try {
    // Get theme details from user
    const themeName = await question('Enter theme name (e.g., "Elegant Portfolio"): ');
    if (!themeName.trim()) {
      console.log('❌ Theme name is required!');
      process.exit(1);
    }
    
    const themeId = await question('Enter theme ID (e.g., "elegant-portfolio-v1"): ');
    if (!themeId.trim()) {
      console.log('❌ Theme ID is required!');
      process.exit(1);
    }
    
    const category = await question('Enter theme category (modern/minimalist/creative/professional): ');
    if (!['modern', 'minimalist', 'creative', 'professional'].includes(category)) {
      console.log('❌ Invalid category! Must be one of: modern, minimalist, creative, professional');
      process.exit(1);
    }
    
    const author = await question('Enter author name (optional): ') || 'Unknown';
    
    console.log('\n🚀 Creating theme...\n');
    
    // Create theme structure
    const { themeDir, componentsDir } = createThemeStructure(themeId, themeName);
    
    // Create CSS file
    createCssFile(themeDir, themeId, themeName);
    
    // Create components
    const componentName = createMainComponent(componentsDir, themeId, themeName);
    createComponentTemplates(componentsDir, themeId, componentName);
    
    // Show next steps
    console.log('\n✅ Theme created successfully!\n');
    console.log('📋 Next steps:');
    console.log(`1. Add your theme to themes/theme-registry.ts:`);
    console.log(`   {`);
    console.log(`     id: '${themeId}',`);
    console.log(`     name: '${themeName}',`);
    console.log(`     description: 'Your theme description',`);
    console.log(`     cssFile: '/themes/${themeId}/${themeId}.css',`);
    console.log(`     sourceCssFile: 'themes/${themeId}/${themeId}.css',`);
    console.log(`     component: ${componentName}Theme,`);
    console.log(`     category: '${category}',`);
    console.log(`     version: '1.0.0',`);
    console.log(`     author: '${author}',`);
    console.log(`   }`);
    console.log(`2. Import your theme component in theme-registry.ts`);
    console.log(`3. Build your theme components and styles`);
    console.log(`4. Run 'npm run sync-themes' to sync CSS files`);
    console.log(`5. Test your theme in the application`);
    
  } catch (error) {
    console.error('❌ Error creating theme:', error.message);
    process.exit(1);
  } finally {
    rl.close();
  }
}

// Run the script
main();
