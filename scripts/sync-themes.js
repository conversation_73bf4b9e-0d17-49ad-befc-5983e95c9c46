#!/usr/bin/env node

/**
 * Theme Sync Script
 *
 * This script compiles modular CSS and syncs CSS files from themes directory
 * to public directory and validates all theme configurations.
 *
 * Usage:
 *   node scripts/sync-themes.js
 *   npm run sync-themes
 */

const fs = require('fs');
const path = require('path');
const { compileCreativeMinimalistCSS } = require('./compile-creative-css.js');
const { compileModernThemeCSS } = require('./compile-modern-css.js');

// Theme registry data (simplified for script usage)
const THEMES = [
  {
    id: 'modern-theme-v1',
    name: 'Modern',
    sourceCssFile: 'themes/modern/modern-compiled.css',
    cssFile: '/themes/modern/modern-compiled.css',
  },
  {
    id: 'creative-theme-v1',
    name: 'Creative Minimalist',
    sourceCssFile: 'themes/creative-minimalist/creative-minimalist-compiled.css',
    cssFile: '/themes/creative-minimalist/creative-minimalist-compiled.css',
  },
];

/**
 * Sync CSS files from themes directory to public directory
 */
function syncThemeCssFiles() {
  console.log('🎨 Compiling and syncing theme CSS files...');

  // First, compile the Creative Minimalist CSS
  console.log('📦 Compiling Creative Minimalist CSS...');
  try {
    compileCreativeMinimalistCSS();
  } catch (error) {
    console.warn('⚠️  Failed to compile Creative Minimalist CSS:', error.message);
  }

  // Then, compile the Modern Theme CSS
  console.log('📦 Compiling Modern Theme CSS...');
  try {
    compileModernThemeCSS();
  } catch (error) {
    console.warn('⚠️  Failed to compile Modern Theme CSS:', error.message);
  }
  
  for (const theme of THEMES) {
    try {
      const sourcePath = path.join(process.cwd(), theme.sourceCssFile);
      const publicPath = path.join(process.cwd(), 'public', theme.cssFile);
      
      // Ensure public directory exists
      const publicDir = path.dirname(publicPath);
      if (!fs.existsSync(publicDir)) {
        fs.mkdirSync(publicDir, { recursive: true });
      }
      
      // Copy source CSS to public directory
      if (fs.existsSync(sourcePath)) {
        fs.copyFileSync(sourcePath, publicPath);
        
        // Get file sizes for comparison
        const sourceStats = fs.statSync(sourcePath);
        const publicStats = fs.statSync(publicPath);
        
        console.log(`✅ Synced ${theme.name}:`);
        console.log(`   Source: ${theme.sourceCssFile} (${sourceStats.size} bytes)`);
        console.log(`   Public: public${theme.cssFile} (${publicStats.size} bytes)`);
      } else {
        console.warn(`⚠️  Source CSS file not found for ${theme.name}: ${sourcePath}`);
      }
    } catch (error) {
      console.error(`❌ Failed to sync CSS for ${theme.name}:`, error.message);
    }
  }
  
  console.log('✨ Theme CSS sync completed!');
}

/**
 * Validate theme files exist
 */
function validateThemeFiles() {
  console.log('\n🔍 Validating theme files...');
  
  let allValid = true;
  
  for (const theme of THEMES) {
    const sourcePath = path.join(process.cwd(), theme.sourceCssFile);
    const publicPath = path.join(process.cwd(), 'public', theme.cssFile);
    
    console.log(`\n📋 Checking ${theme.name}:`);
    
    // Check source file
    if (fs.existsSync(sourcePath)) {
      const stats = fs.statSync(sourcePath);
      console.log(`   ✅ Source CSS: ${theme.sourceCssFile} (${stats.size} bytes)`);
    } else {
      console.log(`   ❌ Source CSS: ${theme.sourceCssFile} (NOT FOUND)`);
      allValid = false;
    }
    
    // Check public file
    if (fs.existsSync(publicPath)) {
      const stats = fs.statSync(publicPath);
      console.log(`   ✅ Public CSS: public${theme.cssFile} (${stats.size} bytes)`);
    } else {
      console.log(`   ❌ Public CSS: public${theme.cssFile} (NOT FOUND)`);
      allValid = false;
    }
    
    // Check if files are in sync
    if (fs.existsSync(sourcePath) && fs.existsSync(publicPath)) {
      const sourceStats = fs.statSync(sourcePath);
      const publicStats = fs.statSync(publicPath);
      
      if (sourceStats.size === publicStats.size && sourceStats.mtime <= publicStats.mtime) {
        console.log(`   ✅ Files are in sync`);
      } else {
        console.log(`   ⚠️  Files may be out of sync (different sizes or timestamps)`);
      }
    }
  }
  
  if (allValid) {
    console.log('\n✅ All theme files are valid!');
  } else {
    console.log('\n❌ Some theme files are missing or invalid!');
    process.exit(1);
  }
}

/**
 * Main function
 */
function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--validate-only')) {
    validateThemeFiles();
  } else {
    syncThemeCssFiles();
    validateThemeFiles();
  }
}

// Run the script
main();
